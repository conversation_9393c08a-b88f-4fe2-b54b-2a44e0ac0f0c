<!DOCTYPE html>
<html lang="zh-CN">
 <head> 
  <meta charset="UTF-8" /> 
  <meta name="description" content="TempMail临时邮箱提供在线免费的一次性电子邮箱服务，它也被称为tempmail、十分钟邮箱、24小时邮箱、可丢弃邮箱，匿名无需注册，保护您的个人电子邮箱免受垃圾邮件的侵害。" /> 
  <meta name="keywords" content="临时邮箱,forsaken mail,disposable mail,免费邮箱,free mail,匿名邮箱,一次性邮箱,邮箱出租,在线邮箱,十分钟邮箱,安全邮箱,临时邮,临时Email,快速注册Email,24Mail" /> 
  <title>临时邮箱 - 十秒钟内收到邮件</title> 
  <link rel="stylesheet" href="//lib.baomitu.com/semantic-ui/2.1.7/semantic.min.css" /> 
  <link rel="stylesheet" href="/css/app.css" /> 
  <link rel="stylesheet" href="/css/prism.css" /> 
 </head> 
 <body> 
  <div class="ui fixed borderless menu"> 
   <div class="ui text container" style="max-width: 100% !important;"> 
    <a href="/" target="_blank"> 
     <div href="#" class="header item"> 
      <img class="logo" src="logo.png" alt="logo" />临时邮箱 
     </div> </a> 
    <div class="ui label item right floated mailaddress" style="width: 35%;"> 
     <button class="ui icon button"> <i class="mail icon copyable"></i> </button> 
     <div class="ui icon input"> 
      <input id="shortid" type="text" placeholder="请等待分配临时邮箱" disabled="" /> 
      <i id="refreshShortid" class="circular refresh link icon"></i> 
     </div> 
     <i id="customShortid" style="margin-left:0.5em" class="circular edit link icon"></i> 
    </div> 
   </div> 
  </div> 
  <div class="ui mains container"> 
   <div class="ui icon message"> 
    <i class="notched circle loading icon"></i> 
    <div class="content"> 
     <div class="header">
       服务正常运行中 
     </div> 
     <p>服务运行正常,请耐心等待系统处理您的来信!</p> 
    </div> 
   </div> 
  </div>
  <div class="ui main container"> 
   <div class="ui grid"> 
    <div class="row"> 
     <div class="column"> 
      <table class="ui celled selectable table"> 
       <thead> 
        <tr> 
         <th>发信人</th> 
         <th>主题</th> 
         <th>时间</th> 
        </tr> 
       </thead> 
       <tbody id="maillist"></tbody> 
      </table> 
     </div> 
    </div> 
    <div class="row"> 
     <div class="column"> 
      <div class="ui card" id="mailcard"> 
       <div class="content"> 
        <i class="code icon right floated"></i> 
        <div class="header">
          我的邮件在哪里？ 
        </div> 
       </div> 
       <div class="content"> 
        <p>等等就来( ͡&deg; ͜ʖ ͡&deg;)</p> 
       </div> 
      </div> 
     </div> 
    </div> 
   </div> 
  </div> 
  <div class="ui hidden divider"></div> 
  <div class="ui container"> 
   <div class="ui piled segment"> 
    <h4 class="ui header">使用规则</h4> 
    <p>0、本服务仅提供临时邮箱接收邮件的功能，不具有发送邮件功能。</p> 
    <p>1、请不要用临时邮箱接收重要内容，临时邮箱只适用于注册一些不重要的网站，防止被骚扰。</p> 
    <p>2、他人可以通过此临时邮箱找回密码，所以注册时应注意个人信息。</p> 
    <p>3、本服务不保证在线率，稳定性和其他技术性和非技术性问题。</p> 
    <p>4、本服务不提供任何支持，使用时造成的直接损失或间接损失不负任何责任。</p> 
    <p>5、如需使用自己的域名，直接创建域名 <strong>A/CNAME</strong> 记录和 <strong>MX</strong> 记录到 <a href="https://tempmail.cn"><strong><u> mx.tempmail.cn </u></strong></a> ，等待生效即可。</p> 
   </div> 
  </div> 
  <div class="ui hidden divider"></div> 
  <div class="ui container"> 
   <div class="ui info message"> 
    <div class="header">
      什么是临时邮箱？ 
    </div> 
    <p><strong>临时邮箱：</strong>是指能接收邮件的临时邮箱，也称为一次性邮箱，24小时邮箱，10分钟邮箱，可丢弃邮箱，是完全匿名和安全的。</p> 
    <p>常常有网站要求我们提供有效的电子邮件地址，以便注册，查看或接收特定内容、服务使用等。但是您以后都不太可能回到这个网站/博客/论坛，使用您的真实邮箱注册的话以后就有可能经常收到垃圾邮件、广告邮件。 处理此问题的一个好方法是在您不确定网站是否完全可信且值得信赖时使用临时的一次性邮件地址。 临时邮件与任何其他电子邮件服务一样，但它是短暂的临时的，用完即自动销毁。使用我们的服务，您将不再担心收到垃圾邮件和其他垃圾邮件，还可以避免被跟踪。</p> 
   </div> 
  </div> 
  <div class="ui hidden divider"></div> 
  <div class="ui container"> 
   <div class="ui message"> 
    <div class="header">
     TG交流群： <a href="https://t.me/TempMail_CN" target="_blank">@TempMail_CN</a> ，本站前端模板：<a href="https://tempmail.cn/public.zip">public.zip</a>
    </div> 
   </div> 
  </div> 
  <br /> 
  <div class="ui modal" id="raw"> 
   <div class="header"></div> 
   <div class="content"></div> 
  </div> 
  <script src="//lib.baomitu.com/jquery/2.1.4/jquery.min.js"></script> 
  <script src="//lib.baomitu.com/semantic-ui/2.1.7/semantic.min.js"></script> 
  <script src="//lib.baomitu.com/socket.io/1.3.7/socket.io.min.js"></script> 
  <script src="//lib.baomitu.com/clipboard.js/1.5.5/clipboard.min.js"></script> 
  <script src="/js/app.js"></script> 
  <script src="/js/prism.js"></script>
  <script async src="https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=ca-pub-2328197016005506"
     crossorigin="anonymous"></script>
  <script>
var _hmt = _hmt || [];
(function() {
  var hm = document.createElement("script");
  hm.src = "https://hm.baidu.com/hm.js?01503d77838079b4d4a76a12d0a748af";
  var s = document.getElementsByTagName("script")[0]; 
  s.parentNode.insertBefore(hm, s);
})();
</script>  
 </body>
</html>